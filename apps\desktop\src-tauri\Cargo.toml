[package]
name = "mixvideo-desktop"
version = "0.2.1"
description = "MixVideo Desktop Application"
authors = ["imeepos <<EMAIL>>"]
edition = "2021"
default-run = "mixvideo-desktop"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "mixvideo_desktop_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = ["protocol-asset"] }
tauri-plugin-opener = "2"
tauri-plugin-fs = "2"
tauri-plugin-dialog = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
serde_yaml = "0.9"
rusqlite = { version = "0.31", features = ["bundled", "chrono"] }
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
tokio = { version = "1.0", features = ["full", "sync"] }
anyhow = "1.0"
thiserror = "1.0"
dirs = "5.0"
md5 = "0.7"
lazy_static = "1.4"
base64 = "0.22"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "chrono"] }
tracing-appender = "0.2"
reqwest = { version = "0.11", features = ["json", "multipart"] }
image = { version = "0.24", features = ["jpeg", "png", "webp", "bmp", "tiff", "gif"] }
toml = "0.8"
tree-sitter = "0.20"
tree-sitter-json = "0.20"
# tree-sitter-markdown = "0.7.1"  # 暂时禁用，存在版本冲突
pulldown-cmark = "0.9"
regex = "1.10"
num_cpus = "1.16"
tokio-tungstenite = "0.20"
futures-util = "0.3"
rand = "0.8"
hmac = "0.12.1"
sha2 = "0.10.9"
hex = "0.4.3"
url = "2.5.4"
urlencoding = "2.1"

[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["sysinfoapi"] }

[dev-dependencies]
tempfile = "3.8"
tokio-test = "0.4"

