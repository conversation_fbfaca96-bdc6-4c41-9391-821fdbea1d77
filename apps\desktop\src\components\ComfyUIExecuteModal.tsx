import React, { useState, useEffect } from 'react';
import { X, Play, FileText, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import ComfyuiService from '../services/comfyuiService';
import type { 
  Workflow, 
  ExecuteWorkflowRequest, 
  ExecuteWorkflowResponse,
  ExecuteWorkflowFormData 
} from '../types/comfyui';

interface ComfyUIExecuteModalProps {
  isOpen: boolean;
  onClose: () => void;
  workflow: Workflow | null;
  onExecutionComplete: (result: ExecuteWorkflowResponse) => void;
}

/**
 * ComfyUI 执行工作流模态框
 */
const ComfyUIExecuteModal: React.FC<ComfyUIExecuteModalProps> = ({
  isOpen,
  onClose,
  workflow,
  onExecutionComplete,
}) => {
  const [formData, setFormData] = useState<ExecuteWorkflowFormData>({
    base_name: '',
    version: '',
    request_data: '{}',
  });
  const [executing, setExecuting] = useState(false);
  const [result, setResult] = useState<ExecuteWorkflowResponse | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showSpec, setShowSpec] = useState(false);
  const [spec, setSpec] = useState<any>(null);
  const [loadingSpec, setLoadingSpec] = useState(false);

  useEffect(() => {
    if (isOpen && workflow) {
      const parsed = ComfyuiService.parseWorkflowName(workflow.name);
      setFormData({
        base_name: parsed.baseName,
        version: parsed.version || '',
        request_data: JSON.stringify({}, null, 2),
      });
      setResult(null);
      setErrors({});
      setSpec(null);
      setShowSpec(false);
    }
  }, [isOpen, workflow]);

  // ============================================================================
  // 表单验证
  // ============================================================================

  const validateForm = (): Record<string, string> => {
    const newErrors: Record<string, string> = {};

    if (!formData.base_name.trim()) {
      newErrors.base_name = '工作流名称不能为空';
    }

    if (!formData.request_data.trim()) {
      newErrors.request_data = '请求数据不能为空';
    } else {
      const validation = ComfyuiService.validateJson(formData.request_data);
      if (!validation.valid) {
        newErrors.request_data = `JSON 格式错误: ${validation.error}`;
      }
    }

    return newErrors;
  };

  // ============================================================================
  // 事件处理
  // ============================================================================

  const handleInputChange = (field: keyof ExecuteWorkflowFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleLoadSpec = async () => {
    if (!formData.base_name.trim()) {
      setErrors({ base_name: '请先输入工作流名称' });
      return;
    }

    setLoadingSpec(true);
    try {
      const specResponse = await ComfyuiService.getWorkflowSpec({
        base_name: formData.base_name,
        version: formData.version || undefined,
      });
      setSpec(specResponse.spec);
      setShowSpec(true);
    } catch (error) {
      setErrors({ spec: `获取工作流规范失败: ${error}` });
    } finally {
      setLoadingSpec(false);
    }
  };

  const handleExecute = async () => {
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setExecuting(true);
    setResult(null);

    try {
      const request: ExecuteWorkflowRequest = {
        base_name: formData.base_name,
        version: formData.version || undefined,
        request_data: JSON.parse(formData.request_data),
      };

      const response = await ComfyuiService.executeWorkflow(request);
      setResult(response);
      onExecutionComplete(response);
    } catch (error) {
      setResult({
        error: `执行失败: ${error}`,
      });
    } finally {
      setExecuting(false);
    }
  };

  const formatJsonData = () => {
    try {
      const parsed = JSON.parse(formData.request_data);
      const formatted = JSON.stringify(parsed, null, 2);
      handleInputChange('request_data', formatted);
    } catch (error) {
      // JSON 格式错误，不做处理
    }
  };

  if (!isOpen || !workflow) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">执行工作流</h2>
            <p className="text-sm text-gray-600 mt-1">工作流: {workflow.name}</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 表单内容 */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 左侧：执行参数 */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">执行参数</h3>
              
              {/* 工作流名称 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  工作流基础名称 *
                </label>
                <input
                  type="text"
                  value={formData.base_name}
                  onChange={(e) => handleInputChange('base_name', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.base_name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="my_workflow"
                />
                {errors.base_name && (
                  <p className="mt-1 text-sm text-red-600">{errors.base_name}</p>
                )}
              </div>

              {/* 版本 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  版本（可选）
                </label>
                <input
                  type="text"
                  value={formData.version}
                  onChange={(e) => handleInputChange('version', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="1.0"
                />
              </div>

              {/* 获取规范按钮 */}
              <div>
                <button
                  onClick={handleLoadSpec}
                  disabled={loadingSpec}
                  className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors disabled:opacity-50"
                >
                  <FileText className={`w-4 h-4 ${loadingSpec ? 'animate-pulse' : ''}`} />
                  {loadingSpec ? '加载中...' : '查看工作流规范'}
                </button>
                {errors.spec && (
                  <p className="mt-1 text-sm text-red-600">{errors.spec}</p>
                )}
              </div>

              {/* 请求数据 */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    请求数据 (JSON) *
                  </label>
                  <button
                    onClick={formatJsonData}
                    className="text-xs text-blue-600 hover:text-blue-700"
                  >
                    格式化
                  </button>
                </div>
                <textarea
                  value={formData.request_data}
                  onChange={(e) => handleInputChange('request_data', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm ${
                    errors.request_data ? 'border-red-300' : 'border-gray-300'
                  }`}
                  rows={8}
                  placeholder='{"param1": "value1", "param2": "value2"}'
                />
                {errors.request_data && (
                  <p className="mt-1 text-sm text-red-600">{errors.request_data}</p>
                )}
              </div>
            </div>

            {/* 右侧：工作流规范或执行结果 */}
            <div className="space-y-4">
              {showSpec && spec ? (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">工作流规范</h3>
                  <div className="bg-gray-50 border border-gray-200 rounded-md p-4 max-h-96 overflow-y-auto">
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                      {JSON.stringify(spec, null, 2)}
                    </pre>
                  </div>
                </div>
              ) : result ? (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">执行结果</h3>
                  <div className={`border rounded-md p-4 ${
                    result.error ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'
                  }`}>
                    {result.error ? (
                      <div className="flex items-start gap-2">
                        <AlertCircle className="w-5 h-5 text-red-500 mt-0.5" />
                        <div>
                          <p className="font-medium text-red-700">执行失败</p>
                          <p className="text-sm text-red-600 mt-1">{result.error}</p>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-5 h-5 text-green-500" />
                          <span className="font-medium text-green-700">执行成功</span>
                        </div>
                        
                        {result.task_id && (
                          <div>
                            <span className="text-sm font-medium text-gray-700">任务ID: </span>
                            <span className="text-sm text-gray-600">{result.task_id}</span>
                          </div>
                        )}
                        
                        {result.status && (
                          <div>
                            <span className="text-sm font-medium text-gray-700">状态: </span>
                            <span className="text-sm text-gray-600">{result.status}</span>
                          </div>
                        )}
                        
                        {result.message && (
                          <div>
                            <span className="text-sm font-medium text-gray-700">消息: </span>
                            <span className="text-sm text-gray-600">{result.message}</span>
                          </div>
                        )}
                        
                        {result.result && (
                          <div>
                            <span className="text-sm font-medium text-gray-700">结果数据:</span>
                            <pre className="text-xs text-gray-600 mt-1 bg-white border border-gray-200 rounded p-2 max-h-32 overflow-y-auto">
                              {JSON.stringify(result.result, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FileText className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p>点击"查看工作流规范"或"执行"查看内容</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            取消
          </button>
          
          <button
            onClick={handleExecute}
            disabled={executing}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
          >
            {executing ? (
              <Clock className="w-4 h-4 animate-spin" />
            ) : (
              <Play className="w-4 h-4" />
            )}
            {executing ? '执行中...' : '执行工作流'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ComfyUIExecuteModal;
