/**
 * ComfyUI API 类型定义
 * 与后端 Rust 数据模型保持一致
 */

// ============================================================================
// 配置相关类型
// ============================================================================

/**
 * ComfyUI API 服务配置
 */
export interface ComfyuiConfig {
  /** API 基础 URL */
  base_url: string;
  /** 请求超时时间（秒） */
  timeout?: number;
  /** 重试次数 */
  retry_attempts?: number;
  /** 是否启用缓存 */
  enable_cache?: boolean;
  /** 最大并发数 */
  max_concurrency?: number;
}

// ============================================================================
// 工作流相关类型
// ============================================================================

/**
 * 工作流对象
 */
export interface Workflow {
  /** 工作流的完整唯一名称，例如 'my_workflow [20250101120000]' */
  name: string;
  /** 工作流的基础名称 */
  base_name?: string;
  /** 工作流版本 */
  version?: string;
  /** 工作流创建时间 */
  created_at?: string;
  /** 工作流更新时间 */
  updated_at?: string;
  /** 工作流描述 */
  description?: string;
  /** 工作流配置数据 */
  [key: string]: any;
}

/**
 * 发布工作流请求
 */
export interface PublishWorkflowRequest {
  /** 工作流名称 */
  name: string;
  /** 工作流配置数据 */
  workflow_data: any;
  /** 工作流描述 */
  description?: string;
  /** 工作流版本 */
  version?: string;
}

/**
 * 发布工作流响应
 */
export interface PublishWorkflowResponse {
  /** 操作是否成功 */
  success: boolean;
  /** 响应消息 */
  message?: string;
  /** 创建的工作流信息 */
  workflow?: Workflow;
}

/**
 * 删除工作流响应
 */
export interface DeleteWorkflowResponse {
  /** 操作是否成功 */
  success: boolean;
  /** 响应消息 */
  message?: string;
  /** 删除的工作流名称 */
  deleted_workflow?: string;
}

// ============================================================================
// 执行相关类型
// ============================================================================

/**
 * 执行工作流请求
 */
export interface ExecuteWorkflowRequest {
  /** 工作流基础名称 */
  base_name: string;
  /** 工作流版本（可选） */
  version?: string;
  /** 请求数据 */
  request_data: any;
}

/**
 * 执行工作流响应
 */
export interface ExecuteWorkflowResponse {
  /** 执行任务ID */
  task_id?: string;
  /** 执行状态 */
  status?: string;
  /** 响应消息 */
  message?: string;
  /** 执行结果数据 */
  result?: any;
  /** 错误信息 */
  error?: string;
}

/**
 * 获取工作流规范请求
 */
export interface GetWorkflowSpecRequest {
  /** 工作流基础名称 */
  base_name: string;
  /** 工作流版本（可选） */
  version?: string;
}

/**
 * 获取工作流规范响应
 */
export interface GetWorkflowSpecResponse {
  /** 工作流规范数据 */
  spec: any;
  /** 工作流名称 */
  name?: string;
  /** 工作流版本 */
  version?: string;
  /** 规范描述 */
  description?: string;
}

// ============================================================================
// 服务器相关类型
// ============================================================================

/**
 * 服务器队列详情
 */
export interface ServerQueueDetails {
  /** 正在运行的任务数量 */
  running_count: number;
  /** 等待中的任务数量 */
  pending_count: number;
}

/**
 * 服务器状态信息
 */
export interface ServerStatus {
  /** 服务器在配置列表中的索引 */
  server_index: number;
  /** HTTP URL */
  http_url: string;
  /** WebSocket URL */
  ws_url: string;
  /** 输入目录路径 */
  input_dir: string;
  /** 输出目录路径 */
  output_dir: string;
  /** 服务器是否可达 */
  is_reachable: boolean;
  /** 服务器是否空闲 */
  is_free: boolean;
  /** 队列详情 */
  queue_details: ServerQueueDetails;
}

/**
 * 文件详情
 */
export interface FileDetails {
  /** 文件名 */
  name: string;
  /** 文件大小（KB） */
  size_kb: number;
  /** 修改时间 */
  modified_at: string;
}

/**
 * 服务器文件信息
 */
export interface ServerFiles {
  /** 服务器在配置列表中的索引 */
  server_index: number;
  /** HTTP URL */
  http_url: string;
  /** 输入文件列表 */
  input_files: FileDetails[];
  /** 输出文件列表 */
  output_files: FileDetails[];
}

// ============================================================================
// API 相关类型
// ============================================================================

/**
 * API 根响应
 */
export interface ApiRootResponse {
  /** API 使用指南 */
  guide: string;
  /** API 版本 */
  version?: string;
  /** 可用端点 */
  endpoints?: string[];
}

/**
 * HTTP 验证错误详情
 */
export interface ValidationError {
  /** 错误位置 */
  loc: any[];
  /** 错误消息 */
  msg: string;
  /** 错误类型 */
  type: string;
}

/**
 * HTTP 验证错误响应
 */
export interface HTTPValidationError {
  /** 错误详情列表 */
  detail: ValidationError[];
}

// ============================================================================
// 错误类型
// ============================================================================

/**
 * ComfyUI API 错误类型
 */
export type ComfyuiError = 
  | { type: 'NetworkError'; message: string }
  | { type: 'HttpError'; status: number; message: string }
  | { type: 'ValidationError'; errors: ValidationError[] }
  | { type: 'ServerError'; message: string }
  | { type: 'WorkflowNotFound'; workflow_name: string }
  | { type: 'ConfigError'; message: string }
  | { type: 'TimeoutError'; message: string }
  | { type: 'UnknownError'; message: string };

// ============================================================================
// UI 状态类型
// ============================================================================

/**
 * 工作流执行状态
 */
export type WorkflowExecutionStatus = 'idle' | 'running' | 'success' | 'error';

/**
 * 服务器连接状态
 */
export type ServerConnectionStatus = 'connected' | 'disconnected' | 'connecting' | 'error';

/**
 * 工作流管理 UI 状态
 */
export interface ComfyuiUIState {
  /** 工作流列表 */
  workflows: Workflow[];
  /** 选中的工作流 */
  selectedWorkflow?: Workflow;
  /** 服务器状态列表 */
  servers: ServerStatus[];
  /** 当前配置 */
  config: ComfyuiConfig;
  /** 连接状态 */
  connectionStatus: ServerConnectionStatus;
  /** 执行状态 */
  executionStatus: WorkflowExecutionStatus;
  /** 加载状态 */
  loading: {
    workflows: boolean;
    servers: boolean;
    execution: boolean;
    config: boolean;
  };
  /** 错误信息 */
  error?: string;
}

/**
 * 工作流表单数据
 */
export interface WorkflowFormData {
  /** 工作流名称 */
  name: string;
  /** 工作流描述 */
  description: string;
  /** 工作流版本 */
  version: string;
  /** 工作流配置数据 */
  workflow_data: string; // JSON 字符串
}

/**
 * 执行工作流表单数据
 */
export interface ExecuteWorkflowFormData {
  /** 工作流基础名称 */
  base_name: string;
  /** 工作流版本 */
  version?: string;
  /** 请求数据 */
  request_data: string; // JSON 字符串
}
