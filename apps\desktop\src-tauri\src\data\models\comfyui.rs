use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;

/// ComfyUI API 服务配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComfyuiConfig {
    /// API 基础 URL
    pub base_url: String,
    /// 请求超时时间（秒）
    pub timeout: Option<u64>,
    /// 重试次数
    pub retry_attempts: Option<u32>,
    /// 是否启用缓存
    pub enable_cache: Option<bool>,
    /// 最大并发数
    pub max_concurrency: Option<u32>,
}

impl Default for ComfyuiConfig {
    fn default() -> Self {
        Self {
            base_url: "https://bowongai-dev--waas-demo-fastapi-webapp.modal.run".to_string(),
            timeout: Some(30),
            retry_attempts: Some(3),
            enable_cache: Some(true),
            max_concurrency: Some(8),
        }
    }
}

/// 工作流对象 - 用于 GET /api/workflow 响应
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Workflow {
    /// 工作流的完整唯一名称，例如 'my_workflow [20250101120000]'
    pub name: String,
    /// 工作流的基础名称
    pub base_name: Option<String>,
    /// 工作流版本
    pub version: Option<String>,
    /// 工作流创建时间
    pub created_at: Option<DateTime<Utc>>,
    /// 工作流更新时间
    pub updated_at: Option<DateTime<Utc>>,
    /// 工作流描述
    pub description: Option<String>,
    /// 工作流配置数据
    #[serde(flatten)]
    pub additional_properties: HashMap<String, serde_json::Value>,
}

/// 发布工作流请求 - 用于 POST /api/workflow
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PublishWorkflowRequest {
    /// 工作流名称
    pub name: String,
    /// 工作流配置数据
    pub workflow_data: serde_json::Value,
    /// 工作流描述
    pub description: Option<String>,
    /// 工作流版本
    pub version: Option<String>,
}

/// 发布工作流响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PublishWorkflowResponse {
    /// 操作是否成功
    pub success: bool,
    /// 响应消息
    pub message: Option<String>,
    /// 创建的工作流信息
    pub workflow: Option<Workflow>,
}

/// 删除工作流响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeleteWorkflowResponse {
    /// 操作是否成功
    pub success: bool,
    /// 响应消息
    pub message: Option<String>,
    /// 删除的工作流名称
    pub deleted_workflow: Option<String>,
}

/// 执行工作流请求 - 用于 POST /api/run/
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecuteWorkflowRequest {
    /// 工作流基础名称
    pub base_name: String,
    /// 工作流版本（可选）
    pub version: Option<String>,
    /// 请求数据
    pub request_data: serde_json::Value,
}

/// 执行工作流响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecuteWorkflowResponse {
    /// 执行任务ID
    pub task_id: Option<String>,
    /// 执行状态
    pub status: Option<String>,
    /// 响应消息
    pub message: Option<String>,
    /// 执行结果数据
    pub result: Option<serde_json::Value>,
    /// 错误信息
    pub error: Option<String>,
}

/// 获取工作流规范请求 - 用于 GET /api/spec/
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetWorkflowSpecRequest {
    /// 工作流基础名称
    pub base_name: String,
    /// 工作流版本（可选）
    pub version: Option<String>,
}

/// 获取工作流规范响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetWorkflowSpecResponse {
    /// 工作流规范数据
    pub spec: serde_json::Value,
    /// 工作流名称
    pub name: Option<String>,
    /// 工作流版本
    pub version: Option<String>,
    /// 规范描述
    pub description: Option<String>,
}

/// 服务器队列详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerQueueDetails {
    /// 正在运行的任务数量
    pub running_count: i32,
    /// 等待中的任务数量
    pub pending_count: i32,
}

/// 服务器状态信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerStatus {
    /// 服务器在配置列表中的索引
    pub server_index: i32,
    /// HTTP URL
    pub http_url: String,
    /// WebSocket URL
    pub ws_url: String,
    /// 输入目录路径
    pub input_dir: String,
    /// 输出目录路径
    pub output_dir: String,
    /// 服务器是否可达
    pub is_reachable: bool,
    /// 服务器是否空闲
    pub is_free: bool,
    /// 队列详情
    pub queue_details: ServerQueueDetails,
}

/// 文件详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileDetails {
    /// 文件名
    pub name: String,
    /// 文件大小（KB）
    pub size_kb: f64,
    /// 修改时间
    pub modified_at: DateTime<Utc>,
}

/// 服务器文件信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerFiles {
    /// 服务器在配置列表中的索引
    pub server_index: i32,
    /// HTTP URL
    pub http_url: String,
    /// 输入文件列表
    pub input_files: Vec<FileDetails>,
    /// 输出文件列表
    pub output_files: Vec<FileDetails>,
}

/// API 根响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiRootResponse {
    /// API 使用指南
    pub guide: String,
    /// API 版本
    pub version: Option<String>,
    /// 可用端点
    pub endpoints: Option<Vec<String>>,
}

/// HTTP 验证错误详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationError {
    /// 错误位置
    pub loc: Vec<serde_json::Value>,
    /// 错误消息
    pub msg: String,
    /// 错误类型
    #[serde(rename = "type")]
    pub error_type: String,
}

/// HTTP 验证错误响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HTTPValidationError {
    /// 错误详情列表
    pub detail: Vec<ValidationError>,
}

/// ComfyUI API 错误类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum ComfyuiError {
    /// 网络连接错误
    NetworkError { message: String },
    /// HTTP 错误
    HttpError { status: u16, message: String },
    /// 验证错误
    ValidationError { errors: Vec<ValidationError> },
    /// 服务器内部错误
    ServerError { message: String },
    /// 工作流不存在
    WorkflowNotFound { workflow_name: String },
    /// 配置错误
    ConfigError { message: String },
    /// 超时错误
    TimeoutError { message: String },
    /// 未知错误
    UnknownError { message: String },
}
